import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import { get } from "lodash";
import { IRoom, RoomStatus } from "../../../Interface/room.interface";
import { useUpdateRoom } from "../../../server-action/API/HotelConfiguration/room";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { Icon } from "@iconify/react/dist/iconify.js";
import cross from "../../../assets/Svg/Cross.svg";

interface RoomStatusEditFormProps {
  room: IRoom;
  onClose: () => void;
  onSuccess?: () => void;
}

const RoomStatusEditValidationSchema = Yup.object().shape({
  status: Yup.string().required("Status is required"),
  notes: Yup.string(),
});

const RoomStatusEditForm: React.FC<RoomStatusEditFormProps> = ({ room, onClose, onSuccess }) => {
  const updateRoomMutation = useUpdateRoom();

  // Check if room can be edited (only cleaning rooms can be changed to available)
  const canEditStatus = room.status === RoomStatus.CLEANING;

  const formik = useFormik({
    initialValues: {
      status: room.status || RoomStatus.CLEANING,
      notes: room.notes || "",
    },
    validationSchema: RoomStatusEditValidationSchema,
    onSubmit: async (values) => {
      try {
        const roomUpdateData = {
          status: values.status as RoomStatus,
          notes: values.notes,
          ...(values.status === RoomStatus.AVAILABLE && { lastCleaned: new Date().toISOString() }),
        };

        await updateRoomMutation.mutateAsync({
          roomData: roomUpdateData,
          _id: room._id!,
        });

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error("Error updating room status:", error);
      }
    },
  });

  // Status options - only show available if current status is cleaning
  const statusOptions = canEditStatus
    ? [
        { value: RoomStatus.CLEANING, label: "Cleaning" },
        { value: RoomStatus.AVAILABLE, label: "Available" },
      ]
    : [
        { value: room.status, label: room.status?.charAt(0).toUpperCase() + room.status?.slice(1) },
      ];

  const formFields = [
    {
      label: "Room Status",
      field: "status",
      type: "dropdown",
      required: true,
      options: statusOptions,
      disabled: !canEditStatus,
      placeholder: "Select Status",
    },
    {
      label: "Notes",
      field: "notes",
      type: "textarea",
      required: false,
      placeholder: "Add any notes about the status change...",
    },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-[#EBFEF4]">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Room {room.roomNo} Status
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <img src={cross} alt="Close" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Room Information */}
            <div>
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Room Summary</h2>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Number</span>
                    <span className="font-medium">{room.roomNo}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Type</span>
                    <span className="font-medium">{get(room, "roomType.name", "N/A")}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Current Status</span>
                    <span className="font-medium capitalize">{room.status}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Floor</span>
                    <span className="font-medium">{room.floor || "N/A"}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Last Cleaned</span>
                    <span className="font-medium">
                      {room.lastCleaned ? new Date(room.lastCleaned).toLocaleDateString() : "N/A"}
                    </span>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t-2 border-gray-300">
                    <span className="text-lg font-semibold text-gray-900">Edit Status</span>
                    <span className={`text-lg font-bold ${
                      canEditStatus ? "text-green-600" : "text-yellow-600"
                    }`}>
                      {canEditStatus ? "Editable" : "View Only"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Status Edit Form */}
            <div>

              <FormikProvider value={formik}>
                <Form className="space-y-4">
              {formFields.map((field) => (
                <div key={field.field}>
                  <FormField
                    label={field.label}
                    name={field.field}
                    type={field.type}
                    options={field.options}
                    required={field.required}
                    disabled={field.disabled}
                    placeholder={field.placeholder}
                    formik={formik}
                  />
                  {field.field === "status" && !canEditStatus && (
                    <p className="text-sm text-gray-500 mt-1">
                      Status can only be changed from "Cleaning" to "Available"
                    </p>
                  )}
                </div>
              ))}

              {/* Status Change Rules */}
              <div className={`border rounded-lg p-4 ${
                canEditStatus ? "bg-green-50 border-green-200" : "bg-yellow-50 border-yellow-200"
              }`}>
                <div className="flex items-start">
                  <Icon
                    icon={canEditStatus ? "mdi:check-circle" : "mdi:information"}
                    className={`mt-0.5 mr-2 ${canEditStatus ? "text-green-600" : "text-yellow-600"}`}
                    width="20"
                    height="20"
                  />
                  <div>
                    <h3 className={`font-medium ${canEditStatus ? "text-green-800" : "text-yellow-800"}`}>
                      Status Change Rules
                    </h3>
                    <p className={`text-sm mt-1 ${canEditStatus ? "text-green-700" : "text-yellow-700"}`}>
                      {canEditStatus
                        ? "This room can be changed from 'Cleaning' to 'Available' once cleaning is completed."
                        : "Only rooms with 'Cleaning' status can be changed to 'Available'. Other status changes are restricted."
                      }
                    </p>
                  </div>
                </div>
              </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-[#6047E4] text-white rounded-md hover:bg-[#5038D3] disabled:opacity-50"
                      disabled={updateRoomMutation.isPending || !canEditStatus}
                    >
                      {updateRoomMutation.isPending ? "Updating..." : "Update Status"}
                    </button>
                  </div>
                </Form>
              </FormikProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomStatusEditForm;
